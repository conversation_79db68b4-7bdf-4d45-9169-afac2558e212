<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>你的姓名 - 求职意向岗位</title>
    <style>
        /* --- 核心设计原则 --- */
        body {
            font-family: "Microsoft YaHei", "黑体", Arial, sans-serif; /* 专业、兼容性好的字体 */
            font-size: 10.5pt; /* 正文字号 */
            line-height: 1.5; /* 1.5倍行距，阅读舒适 */
            background-color: #ffffff;
            color: #333333; /* 主文字颜色，深灰色比纯黑更柔和 */
            margin: 0;
            padding: 0;
        }

        /* --- 简历容器与布局 --- */
        .resume-container {
            max-width: 800px; /* A4纸在屏幕上的常见宽度 */
            min-height: 1050px; /* 模拟A4高度，确保内容在一页内 */
            margin: 20px auto; /* 页面居中 */
            padding: 40px 50px; /* 页边距 */
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1); /* 轻微阴影，增加质感 */
            display: flex;
            flex-direction: column;
        }

        /* --- 模块标题样式 --- */
        h2 {
            font-size: 14pt;
            color: #003366; /* 专业稳重的深蓝色，用于点缀 */
            border-bottom: 2px solid #003366; /* 标题下划线，分割模块 */
            padding-bottom: 5px;
            margin-top: 20px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        /* --- 页眉：个人信息与求职意向 --- */
        header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eeeeee; /* 页眉与主体内容的分隔线 */
        }

        #name {
            font-size: 22pt;
            font-weight: bold;
            color: #003366; /* 姓名突出显示 */
            margin: 0;
        }

        #contact-info {
            font-size: 11pt;
            color: #555;
            margin-top: 10px;
        }
        #contact-info span {
            margin: 0 12px; /* 分隔联系方式 */
        }

        #job-objective {
            margin-top: 15px;
            font-size: 12pt;
            font-weight: bold;
        }
        #job-objective small {
            font-size: 10pt;
            font-weight: normal;
            color: #555;
            margin-left: 10px;
        }
        
        /* --- 内容通用布局：时间和描述 --- */
        .section-item {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            font-weight: bold;
            font-size: 11.5pt;
        }
        .item-header .role {
            font-weight: normal;
            font-style: italic;
            color: #555;
        }
        .item-time {
            white-space: nowrap; /* 确保时间不换行 */
            color: #555;
            font-weight: bold;
        }
        .item-content {
            flex-grow: 1;
        }
        .item-details {
            margin-top: 5px;
        }
        
        /* --- 项目经验/技能列表样式 --- */
        ul {
            padding-left: 20px;
            margin-top: 5px;
            list-style-type: '•  '; /* 使用实心圆点作为项目符号 */
        }
        li {
            margin-bottom: 5px;
        }
        strong {
            font-weight: 600; /* 突出技术名词 */
        }

        /* --- 技能模块特定样式 --- */
        #skills-