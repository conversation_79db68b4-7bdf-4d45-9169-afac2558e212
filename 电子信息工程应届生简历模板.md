# 张三

**电话：** 13812345678 | **邮箱：** <EMAIL> | **意向城市：** 北京/上海

## 求职意向

**嵌入式软件开发工程师**

## 教育背景

**XX大学 | 电子信息工程 | 本科 | 2020.09 - 2024.06**
- **核心课程：** 数字电路、模拟电路、信号与系统、微机原理、C语言程序设计、嵌入式系统开发、数字信号处理
- **GPA：** 3.7/4.0（专业排名：前15%）

## 项目经验

### 基于STM32的智能家居环境监测系统 | 2023.03 - 2023.06 | 项目负责人
- 设计基于STM32F103的环境监测节点，实现温湿度、光照度、气体浓度的实时采集与远程监控
- 使用Altium Designer完成四层PCB设计，负责硬件原理图设计与Layout
- 基于FreeRTOS开发多任务系统，实现传感器数据采集、Wi-Fi通信和本地显示功能
- 采用ESP8266 Wi-Fi模块实现数据上传云平台，响应时间<0.5s，功耗降低30%，设计通过EMC测试

### 车载CAN总线通信系统 | 2022.10 - 2023.01 | 核心开发成员
- 设计基于STM32的车载CAN总线通信节点，实现车内多传感器数据的高可靠传输
- 使用C语言编写CAN驱动程序，实现数据帧的封装、发送、接收与解析
- 设计低功耗模式切换策略，系统待机功耗降至5mA以下
- 开发上位机监控软件（Qt），实现实时数据显示与故障诊断，通信成功率>99.9%

### [课程项目] 数字滤波器设计与实现 | 2022.03 - 2022.06 | 独立完成
- 使用MATLAB设计并仿真FIR与IIR数字滤波器，分析频率响应特性
- 基于TI DSP平台实现实时数字滤波算法，处理音频信号
- 优化算法结构，降低计算复杂度，处理延迟降低40%

## 专业技能

- **编程语言：** C（熟练）, C++（熟悉）, Python（了解）, MATLAB（熟悉）
- **硬件工具：** Altium Designer（原理图与四层板设计）, 示波器, 逻辑分析仪
- **开发平台：** STM32, ESP32/8266, TI DSP
- **软件/开发工具：** Keil MDK, IAR Workbench, FreeRTOS, Git（版本控制）
- **通信协议：** UART, SPI, I2C, CAN, TCP/IP, Wi-Fi

## 奖项与荣誉

- 全国大学生电子设计竞赛省级二等奖（2022.08）
- 校"互联网+"创新创业大赛技术创新奖（2023.05）
- 国家奖学金（2022.10）
- 校级优秀学生（2021.09）