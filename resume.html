<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子信息工程专业简历</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
        }
        body {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            padding: 15mm 20mm;
            font-size: 10pt;
            line-height: 1.5;
            color: #333;
            background-color: #f5f7fa;
        }
        .resume-container {
            background-color: #fff;
            width: 100%;
            height: 100%;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.12);
            border-radius: 12px;
        }
        section {
            background-color: #f9fbff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 18px;
            box-shadow: 0 2px 5px rgba(10,61,98,0.05);
        }
        .header-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e6edf5;
}
        .header {
            text-align: center;
            margin-bottom: 15px;
        }
        h1 {
            font-size: 18pt;
            color: #0a3d62;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .contact-info {
            font-size: 9pt;
            color: #555;
            margin-bottom: 5px;
        }
.objective {
    font-size: 10pt;
    color: #333;
    background-color: #eef2f7;
    padding: 10px 15px;
    border-radius: 6px;
    margin: 15px 0 20px;
    line-height: 1.6;
}
        section {
            margin-bottom: 15px;
        }
        h2 {
            font-size: 13pt;
            color: #0a3d62;
            border-bottom: 2px solid #3c6382;
            padding-bottom: 5px;
            margin-bottom: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #3c6382;
    border-radius: 2px;
}
        .education-item, .project-item, .award-item {
            margin-bottom: 8px;
        }
        .item-title {
            font-weight: bold;
            display: flex;
            justify-content: space-between;
        }
        .item-subtitle {
            color: #555;
            margin-bottom: 3px;
        }
        ul {
            padding-left: 18px;
            margin-bottom: 5px;
        }
        li {
            margin-bottom: 3px;
        }
.skills-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px 20px;
    margin-top: 10px;
}
        .skill-category {
            flex: 1 1 150px;
        }
        .skill-category h3 {
            font-size: 10pt;
            margin-bottom: 3px;
            color: #0a3d62;
        }
        .awards-list {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }
        @media print {
            body {
                width: 100%;
                height: 100%;
                padding: 15mm 20mm;
                margin: 0;
                box-shadow: none;
                background-color: #fff;
            }
            .photo-controls {
                display: none !important;
            }
            @page {
                size: A4;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header-container">
        <div class="photo-container" style="position: absolute; right: 20px; top: 100px; width: 120px; height: 120px; border-radius: 50%; border: 1px solid #dddddd; background-color: #0080ff; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
            <img id="profile-photo" src="imagesmy_photo.jpg" alt="个人照片" style="width: 100%; height: 100%; object-fit: cover; object-position: center; transition: all 0.3s ease;">
        </div>
        <div class="photo-controls" style="position: absolute; right: 20px; top: 150px; width: 200px; background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div style="margin-bottom: 8px;">
                <label for="sizeSlider" style="font-size: 13px; color: #4A5568; vertical-align: middle; min-width: 70px; display: inline-block;">缩放:</label>
                <input type="range" id="sizeSlider" min="0.5" max="1.5" step="0.05" value="1" style="width: 120px; vertical-align: middle;">
            </div>
            <div style="margin-bottom: 8px;">
                <label for="panXSlider" style="font-size: 13px; color: #4A5568; vertical-align: middle; min-width: 70px; display: inline-block;">左右移动:</label>
                <input type="range" id="panXSlider" min="-50" max="50" step="1" value="0" style="width: 120px; vertical-align: middle;">
            </div>
            <div>
                <label for="panYSlider" style="font-size: 13px; color: #4A5568; vertical-align: middle; min-width: 70px; display: inline-block;">上下移动:</label>
                <input type="range" id="panYSlider" min="-50" max="50" step="1" value="0" style="width: 120px; vertical-align: middle;">
            </div>
        </div>
        <div class="header">
            <h1>张汉权</h1>
            <div class="contact-info">
                <i class="fas fa-phone-alt" style="margin-right: 5px; color: #0a3d62;"></i>电话：138****5678
                <span style="margin: 0 10px;">|</span>
                <i class="fas fa-envelope" style="margin-right: 5px; color: #0a3d62;"></i>邮箱：<EMAIL>
                <span style="margin: 0 10px;">|</span>
                <i class="fas fa-map-marker-alt" style="margin-right: 5px; color: #0a3d62;"></i>意向城市：武汉
            </div>
            <div class="objective">求职意向：嵌入式软件开发工程师 | 专注嵌入式系统开发与优化</div>
        </div>
    </div>
    <script>
        // 实现照片调整功能
        const photo = document.getElementById('profile-photo');
        const sizeSlider = document.getElementById('sizeSlider');
        const panXSlider = document.getElementById('panXSlider');
        const panYSlider = document.getElementById('panYSlider');

        // 调整大小
        sizeSlider.addEventListener('input', updatePhotoTransform);

        // 左右移动
        panXSlider.addEventListener('input', updatePhotoTransform);

        // 上下移动
        panYSlider.addEventListener('input', updatePhotoTransform);

        // 初始设置
        function updatePhotoTransform() {
    const scale = parseFloat(sizeSlider.value);
    const limitedScale = Math.min(scale, 1.5); // 限制最大缩放比例为1.5倍
    const translateX = parseFloat(panXSlider.value);
    const translateY = parseFloat(panYSlider.value);
    photo.style.transform = `scale(${limitedScale}) translate(${translateX}px, ${translateY}px)`;
}

updatePhotoTransform();
    </script>

    <section class="education">
        <h2>教育背景</h2>
        <div class="education-item">
            <div class="item-title">
                <span>湖北汽车工业学院</span>
                <span>2021.09 - 2025.06</span>
            </div>
            <div class="item-subtitle">电子信息工程 | 本科 | GPA：3.7/4.0 (专业前10%)</div>
            <div>核心课程：数字电路、模拟电路、信号与系统、单片机原理与应用、嵌入式系统开发、C/C++程序设计、数据结构、通信原理</div>
        </div>
    </section>

    <section class="projects">
        <h2>项目经验</h2>
        <div class="project-item">
            <div class="item-title">
                <span>基于STM32的智能家居环境监测节点 [课程项目]</span>
                <span>2024.03 - 2024.06</span>
            </div>
            <div class="item-subtitle">项目负责人</div>
            <ul>
                <li>Situation: 设计一款低成本、低功耗的智能家居环境监测设备</li>
                <li>Task: 负责硬件原理图设计、PCB Layout及嵌入式软件开发</li>
                <li>Action: 使用Altium Designer完成四层PCB设计；基于Keil MDK开发环境编写传感器驱动程序；实现I2C/SPI通信协议；使用FreeRTOS实现多任务调度</li>
                <li>Result: 成功实现温湿度、光照度实时采集并通过ESP8266上传至云端，响应时间<1s，功耗控制在50mA以下</li>
            </ul>
        </div>
        <div class="project-item">
            <div class="item-title">
                <span>智能小车路径识别与控制系统设计</span>
                <span>2023.09 - 2023.12</span>
            </div>
            <div class="item-subtitle">核心开发成员</div>
            <ul>
                <li>Situation: 开发基于摄像头的智能小车路径识别与自动避障系统</li>
                <li>Task: 负责图像处理算法设计与控制逻辑实现</li>
                <li>Action: 使用OpenCV进行路径识别算法开发；基于STM32F407实现PID速度闭环控制；通过CAN总线实现模块间通信</li>
                <li>Result: 实现小车在复杂路径下的稳定行驶，识别准确率达95%，最高速度达1.2m/s</li>
            </ul>
        </div>
    </section>

    <section class="skills">
        <h2>专业技能</h2>
        <div class="skills-container">
            <div class="skill-category">
                <h3>编程语言</h3>
                <div>C/C++ (熟练)、Python (熟悉)、Verilog (了解)</div>
            </div>
            <div class="skill-category">
                <h3>硬件开发</h3>
                <div>STM32系列、Arduino、ESP32/8266、Altium Designer</div>
            </div>
            <div class="skill-category">
                <h3>软件开发</h3>
                <div>Keil MDK、IAR Embedded Workbench、Linux (Ubuntu)、FreeRTOS</div>
            </div>
            <div class="skill-category">
                <h3>通信协议</h3>
                <div>UART、SPI、I2C、CAN、TCP/IP、WiFi</div>
            </div>
        </div>
    </section>

    <section class="awards">
        <h2>奖项与荣誉</h2>
        <div class="awards-list">
            <div>• 校级电子设计竞赛一等奖 | 2024.06</div>
            <div>• 国家励志奖学金 | 2023.11</div>
            <div>• 校级优秀学生干部 | 2023.05</div>
            <div>• 全国大学生数学建模竞赛省级二等奖 | 2022.12</div>
        </div>
    </section>
</body>
</html>