# 个人信息
**张汉权**
电话：135XXXXXXXX
邮箱：<EMAIL>
意向城市：武汉

# 求职意向
嵌入式软件开发工程师 | 专注于嵌入式系统开发与优化

# 教育背景
**湖北汽车工业学院** | 电子信息工程 | 本科 | 2021.09-2025.06(预计)
**核心课程**：数字电路(92)、模拟电路(88)、信号与系统(90)、单片机原理及应用(95)、嵌入式系统开发(93)、C语言程序设计(94)、数据结构(89)、通信原理(87)
**GPA**：3.8/4.0 | 专业排名：前5%

# 项目经验
## 基于STM32的智能家居环境监测节点设计 | 2024.03-2024.06
**项目描述**：设计并实现一个集温湿度、光照度采集与远程传输的智能家居节点
**负责工作**：
• 独立完成硬件原理图设计与四层PCB Layout，使用Altium Designer进行布局布线
• 开发STM32微控制器固件，使用C语言实现传感器(DHT11, BH1750)驱动程序
• 基于FreeRTOS实现多任务调度，优化系统响应时间
• 通过ESP8266模块实现数据Wi-Fi传输，完成与云端平台对接
**项目成果**：
• 实现环境参数实时采集，数据更新周期<1s，功耗控制在30mA以内
• 设计通过EMC测试，硬件成本降低15%
• 获得校级电子设计竞赛一等奖

## 智能小车控制系统开发 [课程项目] | 2023.09-2023.12
**项目描述**：开发基于51单片机的循迹避障智能小车系统
**负责工作**：
• 设计控制系统硬件电路，包括传感器模块、电机驱动模块
• 使用C语言编写控制程序，实现PID算法进行速度闭环控制
• 调试红外循迹传感器与超声波避障模块
**项目成果**：
• 实现小车在复杂路径下的稳定循迹，最高速度达0.8m/s
• 成功避障响应时间<200ms

# 专业技能
**编程语言**：C(熟练)、C++(熟悉)、Python(了解)、Verilog(基础)
**硬件开发**：
• 开发工具：Altium Designer(熟练)、Multisim(熟悉)、Keil MDK(熟练)
• 微控制器：STM32系列(熟练)、51单片机(熟悉)、ESP32(了解)
• 仪器使用：示波器、信号发生器、逻辑分析仪(熟练操作)
**软件开发**：
• 操作系统：Linux(Ubuntu)、FreeRTOS(熟悉)
• 开发工具：Git(版本控制)、VS Code(熟悉)
• 通信协议：UART、I2C、SPI、CAN、TCP/IP(了解)

# 奖项与荣誉
• 全国大学生电子设计竞赛省级二等奖 | 2024.08
• 校级一等奖学金(专业前5%) | 2022-2024(连续三年)
• 校级电子设计竞赛一等奖 | 2024.06
• 优秀学生干部 | 2023.05