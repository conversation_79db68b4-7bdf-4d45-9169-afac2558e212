<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历 - 电子信息工程</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html-docx-js@0.3.1/dist/html-docx.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%);
            color: #2d3748;
            line-height: 1.35;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .resume-container {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            background: white;
            position: relative;
            padding: 20px;
            display: grid;
            grid-template-rows: auto 1fr auto;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            grid-template-areas:
                "header header"
                "left right"
                "footer footer";
        }
        
        .header {
            grid-area: header;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #3182ce;
        }
        
        .profile {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .profile-icon {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: #0080FF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: white;
            font-size: 36px;
            overflow: hidden;
        }
        
        .profile-info {
            flex: 1;
        }
        
        .name {
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 2px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #3182ce;
            margin-bottom: 5px;
        }
        
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            font-size: 14px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .education-summary {
            text-align: right;
            padding: 10px 0;
        }
        
        .education-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            color: #2d3748;
        }
        
        .education-details {
            font-size: 14px;
        }
        
        .section {
            margin-bottom: 5px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #3182ce;
            padding-bottom: 3px;
            margin-bottom: 8px;
            border-bottom: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            width: 24px;
            text-align: center;
        }
        
        .left-column {
            grid-area: left;
            padding-right: 15px;
            border-right: 1px solid #e2e8f0;
        }
        
        .right-column {
            grid-area: right;
            padding-left: 15px;
        }
        
        .item {
            margin-bottom: 6px;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .item-title {
            font-size: 15px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .item-date {
            font-size: 13px;
            color: #4a5568;
            font-weight: 500;
        }
        
        .item-details {
            font-size: 13.5px;
            padding-left: 5px;
            line-height: 1.25;
        }
        
        .item-details ul {
            padding-left: 20px;
            list-style-position: outside;
            margin-top: 3px;
        }
        
        .item-details p {
            margin-bottom: 1px;
        }
        
        .skills-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }
        
        .skill-category {
            margin-bottom: 6px;
        }
        
        .skill-category h4 {
            font-size: 15px;
            color: #2d3748;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .skill {
            background: #ebf8ff;
            color: #3182ce;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12.5px;
            font-weight: 500;
        }
        
        .footer {
            grid-area: footer;
            text-align: center;
            padding-top: 10px;
            margin-top: 8px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 13px;
        }
        
        .awards-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 6px;
        }
        
        .award-item {
            padding: 6px;
            background: #f7fafc;
            border-radius: 6px;
            border-left: 3px solid #3182ce;
        }
        
        .compact-list {
            padding-left: 18px;
        }
        
        .compact-list li {
            margin-bottom: 3px;
            font-size: 13.5px;
        }
        
        @media print {
            @page {
                size: auto; /* 或者指定如 A4, letter */
                margin: 10mm; /* 设置打印边距，您可以根据需要调整 */

                /* 尝试通过清空页边框内容来移除浏览器默认的页眉页脚 */
                @top-left      { content: ""; }
                @top-center    { content: ""; }
                @top-right     { content: ""; }
                @bottom-left   { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right  { content: ""; }
            }

            body {
                background: white !important; /* 确保打印背景为白色 */
                padding: 0;
                margin: 0; /* 移除body的默认边距 */
                -webkit-print-color-adjust: exact; /* 帮助WebKit浏览器打印背景色 */
                print-color-adjust: exact; /* 标准属性，用于打印背景色 */
            }

            .resume-container {
                box-shadow: none !important; /* 打印时移除阴影 */
                width: 100%; /* 宽度占满@page边距定义的区域 */
                height: auto; /* 高度自适应 */
                padding: 15px; /* 保留原有的打印内边距 */
                margin: 0 auto; /* 在打印区域内居中 */
                border-radius: 0; /* 可选：打印时移除圆角 */
            }

            .avatar-controls {
                display: none !important; /* 新增：打印时隐藏头像缩放控件 */
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <header class="header">
            <div class="profile">
                <div class="profile-icon">
                    <img id="avatarImage" src="imagesmy_photo.jpg" alt="个人头像" style="display: block; max-width: 100%; max-height: 100%; width: auto; height: auto; margin: auto; transform-origin: center center;">
                </div>
                <div class="profile-info">
                    <h1 class="name">张汉权</h1>
                    <div class="title">电子信息工程 | 预备党员</div>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-university"></i>
                            <span>湖北汽车工业学院</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span>2022级本科生</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-star"></i>
                            <span>GPA: 3.476 | 专业课85+</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>13235651045</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>湖北十堰</span>
                        </div>
                    </div>
                    <div class="avatar-controls" style="margin-top: 10px; display: flex; flex-direction: column; gap: 5px;">
                         <div>
                            <label for="avatarScaleSlider" style="font-size: 13px; color: #4A5568; vertical-align: middle; min-width: 70px; display: inline-block;">缩放:</label>
                            <input type="range" id="avatarScaleSlider" min="0.5" max="2.0" step="0.05" value="1" style="width: 130px; vertical-align: middle; margin-left: 5px;">
                         </div>
                         <div>
                            <label for="avatarPanXSlider" style="font-size: 13px; color: #4A5568; vertical-align: middle; min-width: 70px; display: inline-block;">左右移动:</label>
                            <input type="range" id="avatarPanXSlider" min="-50" max="50" step="1" value="0" style="width: 130px; vertical-align: middle; margin-left: 5px;">
                         </div>
                         <div>
                            <label for="avatarPanYSlider" style="font-size: 13px; color: #4A5568; vertical-align: middle; min-width: 70px; display: inline-block;">上下移动:</label>
                            <input type="range" id="avatarPanYSlider" min="-50" max="50" step="1" value="0" style="width: 130px; vertical-align: middle; margin-left: 5px;">
                         </div>
                         <div style="margin-top: 10px;">
                            <button id="downloadWordButton" style="padding: 8px 15px; background-color: #3182ce; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 13px;">
                                <i class="fas fa-file-word"></i> 下载为Word文档
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="education-summary">
                <div class="education-title">教育背景</div>
                <div class="education-details">
                    <p>电子信息工程</p>
                    <p>2022年9月 - 至今</p>
                </div>
            </div>
        </header>
        
        <div class="left-column">
            <!-- 专业技能 -->
            <section class="section">
                <h3 class="section-title"><i class="fas fa-laptop-code"></i>专业技能</h3>
                <div class="skills-container">
                    <div class="skill-category">
                        <h4><i class="fas fa-microchip"></i>电子与嵌入式</h4>
                        <div class="skills">
                            <div class="skill">单片机开发</div>
                            <div class="skill">STM32</div>
                            <div class="skill">嵌入式系统</div>
                            <div class="skill">电路设计</div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4><i class="fas fa-code"></i>编程语言</h4>
                        <div class="skills">
                            <div class="skill">C/C++</div>
                          
                            <div class="skill">MATLAB</div>
                            <div class="skill">Verilog HDL</div>
                            <div class="skill">Java</div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4><i class="fas fa-tools"></i>开发工具</h4>
                        <div class="skills">
                            <div class="skill">Altium Designer</div>
                            <div class="skill">Keil</div>
                            <div class="skill">Proteus</div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4><i class="fas fa-certificate"></i>证书</h4>
                        <div class="skills">
                            <div class="skill">计算机二级(MS Office)</div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 奖项荣誉 -->
            <section class="section">
                <h3 class="section-title"><i class="fas fa-award"></i>奖项荣誉</h3>
                <div class="awards-grid">
                    <div class="award-item">
                        <div class="item-header">
                            <div class="item-title">嵌入式芯片与系统设计竞赛</div>
                            <div class="item-date">2023年8月</div>
                        </div>
                        <div class="item-details">
                            <p>省级二等奖 | 冷库环境检测系统 <!-- (可在此处简述您的个人贡献或职责) --></p>
                        </div>
                    </div>
                    
                    <div class="award-item">
                        <div class="item-header">
                            <div class="item-title">嵌入式芯片与系统设计竞赛</div>
                            <div class="item-date">2024年8月</div>
                        </div>
                        <div class="item-details">
                            <p>省级三等奖 | 智能充电系统 <!-- (可在此处简述您的个人贡献或职责) --></p>
                        </div>
                    </div>
                    
                    <div class="award-item">
                        <div class="item-header">
                            <div class="item-title">互联网+创新创业大赛</div>
                            <div class="item-date">2023年8月</div>
                        </div>
                        <div class="item-details">
                            <p>校级二等奖 | 国产自主可控冷库监控系统 <!-- (可在此处简述您的个人贡献或职责) --></p>
                        </div>
                    </div>
                    
                    <div class="award-item">
                        <div class="item-header">
                            <div class="item-title">优秀共青团员</div>
                            <div class="item-date">多次获得</div>
                        </div>
                        <div class="item-details">
                            <p>因表现突出，多次被评为优秀共青团员</p>
                        </div>
                    </div>
                    
                    <div class="award-item">
                        <div class="item-header">
                            <div class="item-title">人民奖学金</div>
                            <div class="item-date">2023-2024</div>
                        </div>
                        <div class="item-details">
                            <p>三次获得 | 学业成绩前10%</p>
                        </div>
                    </div>
                </div>
            </section>

            <section class="section">
                <h3 class="section-title"><i class="fas fa-user-check"></i>自我评价</h3>
                <div class="item-details" style="padding-left: 5px; font-size: 13.5px; line-height: 1.35;">
                     <p style="margin-bottom: 4px;"><i class="fas fa-circle" style="color: #3182ce; margin-right: 7px; font-size: 0.6em; vertical-align: middle;"></i>具备扎实的专业知识和动手能力，对新技术充满热情，学习能力强。</p>
                     <p style="margin-bottom: 4px;"><i class="fas fa-circle" style="color: #3182ce; margin-right: 7px; font-size: 0.6em; vertical-align: middle;"></i>拥有良好的沟通协调能力和团队合作精神，责任心强，能承受工作压力。</p>
                     <p><i class="fas fa-circle" style="color: #3182ce; margin-right: 7px; font-size: 0.6em; vertical-align: middle;"></i>积极参与实践，注重理论与实践结合，具备分析和解决问题能力。</p>
                </div>
            </section>

            <!-- 学术成就 -->
            <section class="section">
                <h3 class="section-title"><i class="fas fa-book"></i>学术成就</h3>
                <div class="item">
                    <div class="item-header">
                        <div class="item-title">专业课程</div>
                    </div>
                    <div class="item-details">
                        <p>• 专业课成绩全部85分以上</p>
                        <p>• 核心课程：嵌入式系统设计、单片机原理、信号与系统、通信原理</p>
                    </div>
                </div>
            </section>
        </div>
        
        <div class="right-column">
            <!-- 求职意向 -->
            <section class="section">
                <h3 class="section-title"><i class="fas fa-bullseye"></i>求职意向</h3>
                <div class="item">
                    <div class="item-details" style="padding-left: 5px; font-size: 13.5px; line-height: 1.35;">
                        <p><strong>意向岗位：</strong> 嵌入式系统工程师 / 硬件工程师 </p>
                        <p><strong>期望城市：</strong> 武汉 / 深圳 / 上海 </p>
                        <p><strong>期望薪资：</strong> 9K-15K</p>
                        <p><strong>到岗时间：</strong> 可立即到岗 </p>
                    </div>
                </div>
            </section>

            <!-- 项目经历 -->
            <section class="section">
                <h3 class="section-title"><i class="fas fa-project-diagram"></i>项目经历</h3>
                <div class="item">
                    <div class="item-header">
                        <div class="item-title">冷库环境检测系统</div>
                      
                    </div>
                    <div class="item-details">
                        <p><strong>项目背景：</strong>解决传统冷库监控不足及数据追溯难题，开发智能监控系统提升冷链效率及品质保障。</p>
                        <p>• <strong>主导设计并实现</strong>基于STM32的嵌入式系统，用于冷库多点温湿度精确监控</p>
                        <p>• <strong>成功实现</strong>关键环境参数（温度、湿度、气体浓度）的实时采集与超限报警功能</p>
                        <p>• <strong>独立开发</strong>远程监控与数据可视化平台，支持历史数据查询与报表生成</p>
                        <p>• <strong>优化设计</strong>系统功耗管理，延长设备在备用电源下的运行时间约30%</p>
                    </div>
                </div>
                
                <div class="item">
                    <div class="item-header">
                        <div class="item-title">智能充电系统</div>
                     
                    </div>
                    <div class="item-details">
                        <p><strong>项目背景：</strong>应对新能源车充电需求增长及充电桩分布/效率问题，研发安全高效的智能充电方案。</p>
                        <p>• <strong>负责</strong>核心充电控制模块与用户认证计费系统的设计与实现</p>
                        <p>• <strong>成功集成</strong>多种支付方式（如扫码支付、IC卡）并确保交易安全</p>
                        <p>• <strong>开发</strong>基于云平台的远程监控与管理系统，支持充电桩状态实时查看与远程控制</p>
                        <p>• <strong>实现</strong>智能负载均衡与预约充电功能，提升充电效率与用户体验</p>
                    </div>
                </div>
                
        
                
                <div class="item">
                    <div class="item-header">
                        <div class="item-title">基于CAN总线的车载虚拟仪表</div>
                        
                    </div>
                    <div class="item-details">
                        <p><strong>项目背景：</strong>针对传统仪表盘信息显示与交互局限，设计CAN总线虚拟仪表原型，探索其在数据显示、诊断及交互的应用。</p>
                        <p>• <strong>自主设计、焊接并调试</strong>以STM32为核心的智能小车，集成多路传感器（温湿度、转速、电压）并实现本地数据显示。</p>
                        <p>• <strong>主导设计CAN通信协议</strong>（.dbc），实现STM32与CANPRO上位机间稳定高效的数据交互与车辆状态信息传输。</p>
                        <p>• <strong>独立开发CANPRO上位机虚拟仪表</strong>，动态可视化关键参数，实现上位机对小车电机与LED的远程控制及PID调速优化。</p>
                    </div>
                </div>
            </section>
            
            <!-- 兴趣爱好 (Moved and reformatted) -->
            <section class="section">
                <h3 class="section-title"><i class="fas fa-heart"></i>兴趣爱好</h3>
                <div class="item-details" style="padding-left: 5px; font-size: 13.5px; line-height: 1.35;">
                    <p>热爱篮球、羽毛球等体育运动，业余时间喜欢阅读技术博客与科幻小说，并对探索开源项目充满热情。</p>
                </div>
            </section>

            <!-- 政治面貌 -->
            
        </div>
        
        <footer class="footer">
    
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const avatarImage = document.getElementById('avatarImage');
            const avatarScaleSlider = document.getElementById('avatarScaleSlider');
            const avatarPanXSlider = document.getElementById('avatarPanXSlider');
            const avatarPanYSlider = document.getElementById('avatarPanYSlider');

            let currentScale = 1;
            let currentPanX = 0;
            let currentPanY = 0;

            function updateAvatarTransform() {
                if (avatarImage) {
                    avatarImage.style.transform = `translateX(${currentPanX}px) translateY(${currentPanY}px) scale(${currentScale})`;
                }
            }

            if (avatarImage && avatarScaleSlider && avatarPanXSlider && avatarPanYSlider) {
                currentScale = parseFloat(avatarScaleSlider.value);
                currentPanX = parseInt(avatarPanXSlider.value);
                currentPanY = parseInt(avatarPanYSlider.value);
                updateAvatarTransform(); // Initial transform based on slider default values

                avatarScaleSlider.addEventListener('input', function() {
                    currentScale = parseFloat(this.value);
                    updateAvatarTransform();
                });

                avatarPanXSlider.addEventListener('input', function() {
                    currentPanX = parseInt(this.value);
                    updateAvatarTransform();
                });

                avatarPanYSlider.addEventListener('input', function() {
                    currentPanY = parseInt(this.value);
                    updateAvatarTransform();
                });
            }

            // Function to convert image URL to Base64
            function convertImageToBase64(imgElement, callback) {
                const imgSrc = imgElement.src;
                if (!imgSrc) {
                    callback(null);
                    return;
                }

                // Prevent CORS issues if the image is from the same origin or already a data URL
                if (imgSrc.startsWith('data:image')) {
                    callback(imgSrc);
                    return;
                }

                // For external images, this will be tricky due to CORS.
                // If images are on the same domain, it should work.
                // For this example, we'll assume same-origin or that CORS is handled.
                // A more robust solution for external images might require a server-side proxy.
                try {
                    const canvas = document.createElement("canvas");
                    const ctx = canvas.getContext("2d");
                    const originalImage = new Image();
                    originalImage.crossOrigin = "Anonymous"; // Attempt to handle CORS for external images

                    originalImage.onload = () => {
                        canvas.width = originalImage.naturalWidth;
                        canvas.height = originalImage.naturalHeight;
                        ctx.drawImage(originalImage, 0, 0);
                        try {
                            const dataURL = canvas.toDataURL("image/png");
                            callback(dataURL);
                        } catch (e) {
                            console.error("Canvas toDataURL error:", e);
                            // Fallback: return original src, html-docx-js might not render it
                            callback(imgSrc); 
                        }
                    };
                    originalImage.onerror = () => {
                        console.error("Error loading image for base64 conversion:", imgSrc);
                        callback(imgSrc); // Fallback to original src
                    };
                    originalImage.src = imgSrc;
                } catch (e) {
                    console.error("Error in convertImageToBase64 setup:", e);
                    callback(imgSrc); // Fallback
                }
            }

            const downloadWordButton = document.getElementById('downloadWordButton');
            if (downloadWordButton) {
                downloadWordButton.addEventListener('click', async function() {
                    const resumeContainer = document.querySelector('.resume-container').cloneNode(true);

                    // Remove controls that shouldn't be in the Word document
                    const avatarControls = resumeContainer.querySelector('.avatar-controls');
                    if (avatarControls) {
                        avatarControls.remove();
                    }
                    const downloadButtonInContent = resumeContainer.querySelector('#downloadWordButton'); // if it somehow gets cloned
                    if (downloadButtonInContent && downloadButtonInContent.parentElement.isEqualNode(this.parentElement)) {
                         downloadButtonInContent.parentElement.remove();
                    }


                    // Convert avatar image to base64
                    const avatarImgElement = resumeContainer.querySelector('#avatarImage');
                    if (avatarImgElement) {
                        await new Promise(resolve => {
                            convertImageToBase64(avatarImgElement, function(base64Image) {
                                if (base64Image) {
                                    avatarImgElement.src = base64Image;
                                }
                                // Also remove transform style from image as it might not render well in Word
                                avatarImgElement.style.transform = ''; 
                                resolve();
                            });
                        });
                    }
                    
                    // It's important to provide the full HTML structure including styles for html-docx-js
                    // We will grab all style rules from the document.
                    let styles = '';
                    for (let i = 0; i < document.styleSheets.length; i++) {
                        try {
                            const sheet = document.styleSheets[i];
                            // Check if the sheet is a CSSStyleSheet and has rules
                            if (sheet instanceof CSSStyleSheet && sheet.cssRules) {
                                for (let j = 0; j < sheet.cssRules.length; j++) {
                                    styles += sheet.cssRules[j].cssText + '\\n';
                                }
                            } else if (sheet.href) { // For linked stylesheets, we can't directly access rules if they are cross-origin
                                // In a real-world scenario, you might fetch these if same-origin or pre-bundle.
                                // For now, this part might miss external stylesheet contents if CORS restricted.
                                console.warn("Cannot access rules for linked stylesheet (CORS):", sheet.href);
                            }
                        } catch (e) {
                            console.warn("Could not process stylesheet: ", e);
                        }
                    }

                    const fullHtml = `
                        <!DOCTYPE html>
                        <html lang="zh-CN">
                        <head>
                            <meta charset="UTF-8">
                            <title>个人简历</title>
                            <style>
                                ${styles}
                                /* Additional specific styles for DOCX output if needed */
                                body { margin: 20px; font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; }
                                .resume-container { border: none !important; box-shadow: none !important; width: 100% !important; height: auto !important; }
                                /* Hide controls that might have been missed if any */
                                .avatar-controls, #downloadWordButton { display: none !important; }
                            </style>
                        </head>
                        <body>
                            ${resumeContainer.outerHTML}
                        </body>
                        </html>
                    `;

                    try {
                        const converted = htmlDocx.asBlob(fullHtml, {
                            orientation: 'portrait',
                            margins: { top: 720, right: 720, bottom: 720, left: 720 } // 1 inch margins
                        });
                        saveAs(converted, '个人简历-张汉权.docx');
                    } catch (e) {
                        console.error("Error converting to DOCX:", e);
                        alert("抱歉，生成Word文档时出错。请检查控制台获取更多信息。");
                    }
                });
            }
        });
    </script>
</body>
</html>