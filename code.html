<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子信息工程专业 - 应届生简历</title>
    <style>
        /* 全局样式 - 符合专业简历规范 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        /* A4纸尺寸规范 */
        .resume-container {
            width: 210mm;
            min-height: 297mm;
            background: white;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            padding: 15mm;
        }

        /* 照片定位系统 - 通过CSS变量控制位置 */
        .profile-photo {
            position: absolute;
            width: 36mm; /* 标准一寸照片尺寸 */
            height: 48mm;
            border: 1px solid #eaeaea;
            overflow: hidden;
            /* 通过修改变量值调整位置 */
            top: var(--photo-top, 15mm);
            right: var(--photo-right, 15mm);
            z-index: 10;
        }
        
        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 头部信息 */
        .header {
            padding-bottom: 10px;
            border-bottom: 2px solid #1a3a6d;
            margin-bottom: 5mm;
        }
        
        .name {
            font-size: 24pt;
            font-weight: 700;
            color: #1a3a6d;
            letter-spacing: 1px;
        }
        
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
            font-size: 10pt;
            color: #555;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
        }

        /* 模块标题 */
        .section-title {
            font-size: 14pt;
            color: #1a3a6d;
            font-weight: 700;
            margin: 6mm 0 3mm;
            padding-bottom: 2mm;
            border-bottom: 1px solid #eaeaea;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 35mm;
            height: 2px;
            background: #1a3a6d;
        }

        /* 教育背景 */
        .education-header {
            display: flex;
            justify-content: space-between;
            font-weight: 600;
            font-size: 11pt;
        }
        
        .degree {
            font-size: 10.5pt;
            color: #444;
            margin: 1mm 0 2mm;
        }
        
        .courses {
            display: flex;
            flex-wrap: wrap;
            gap: 3mm;
            margin-top: 2mm;
        }
        
        .course-item {
            background: #f0f4f9;
            padding: 1mm 3mm;
            border-radius: 2mm;
            font-size: 9.5pt;
            color: #1a3a6d;
        }

        /* 项目经验 - 使用STAR原则 */
        .project-item {
            margin-bottom: 4mm;
            page-break-inside: avoid;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }
        
        .project-name {
            font-weight: 600;
            font-size: 10.5pt;
            color: #333;
        }
        
        .project-role {
            font-size: 10pt;
            color: #555;
            font-style: italic;
            margin-bottom: 1mm;
        }
        
        .project-details li {
            margin-bottom: 1.5mm;
            font-size: 10pt;
            line-height: 1.4;
            color: #444;
            text-align: justify;
        }
        
        .star-label {
            font-weight: 600;
            color: #1a3a6d;
        }

        /* 专业技能 */
        .skills-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2mm 5mm;
        }
        
        .skill-category {
            margin-bottom: 1mm;
        }
        
        .skill-title {
            font-size: 10.5pt;
            font-weight: 600;
            color: #1a3a6d;
            margin-bottom: 1mm;
        }
        
        .skill-items {
            list-style-type: none;
            font-size: 10pt;
        }
        
        .skill-items li {
            margin-bottom: 0.5mm;
            position: relative;
            padding-left: 4mm;
        }
        
        .skill-items li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #1a3a6d;
        }

        /* 打印优化 */
        @media print {
            body {
                background: none;
                padding: 0;
            }
            .resume-container {
                box-shadow: none;
                margin: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 右上角照片 - 位置可通过CSS变量调整 -->
        <div class="profile-photo" style="--photo-top: 15mm; --photo-right: 15mm;">
            <img src="file:///D:/desktop/简历1/images/my_photo.jpg" alt="专业证件照">
        </div>
        
        <!-- 头部信息 -->
        <div class="header">
            <h1 class="name">张明轩</h1>
            <div class="contact-info">
                <div class="contact-item">📱 138-XXXX-XXXX</div>
                <div class="contact-item">✉️ <EMAIL></div>
                <div class="contact-item">🎓 上海科技大学 | 电子信息工程 | 应届本科</div>
                <div class="contact-item">📍 求职意向：嵌入式软件开发工程师（上海/深圳）</div>
            </div>
        </div>
        
        <!-- 教育背景 -->
        <div class="section">
            <h2 class="section-title">教育背景</h2>
            <div class="education-item">
                <div class="education-header">
                    <span>上海科技大学 · 电子信息工程学院</span>
                    <span>2020.09 - 2024.06</span>
                </div>
                <p class="degree">工学学士 | GPA：3.8/4.0（专业前15%）| 主修课程平均分：92</p>
                <div class="courses">
                    <span class="course-item">嵌入式系统开发(94)</span>
                    <span class="course-item">STM32原理与应用(93)</span>
                    <span class="course-item">FPGA设计(92)</span>
                    <span class="course-item">数字信号处理(91)</span>
                    <span class="course-item">通信原理(90)</span>
                    <span class="course-item">C/C++程序设计(95)</span>
                </div>
            </div>
        </div>
        
        <!-- 项目经验 -->
        <div class="section">
            <h2 class="section-title">项目经验</h2>
            
            <div class="project-item">
                <div class="project-header">
                    <span class="project-name">智能家居环境监测系统</span>
                    <span class="project-date">2023.03 - 2023.06</span>
                </div>
                <p class="project-role">项目负责人｜硬件设计 & 嵌入式开发</p>
                <ul class="project-details">
                    <li><span class="star-label">S：</span>针对家庭环境监测需求，设计可联网的多节点监测系统，实现温湿度、光照等参数实时采集</li>
                    <li><span class="star-label">T：</span>独立完成STM32F407硬件设计，开发传感器驱动，实现WiFi数据传输与低功耗管理</li>
                    <li><span class="star-label">A：</span>采用Altium Designer设计四层PCB｜Keil MDK开发环境｜FreeRTOS任务调度｜ESP8266透传｜功耗优化设计</li>
                    <li><span class="star-label">R：</span>系统响应时间&lt;800ms｜待机功耗0.5mA｜通过EMC测试｜获校级电子设计竞赛一等奖</li>
                </ul>
            </div>
            
            <div class="project-item">
                <div class="project-header">
                    <span class="project-name">基于FPGA的实时信号处理系统</span>
                    <span class="project-date">2022.09 - 2022.12</span>
                </div>
                <p class="project-role">核心开发｜FPGA逻辑设计 & 算法实现</p>
                <ul class="project-details">
                    <li><span class="star-label">S：</span>开发支持实时音频处理的硬件系统，需满足高吞吐量、低延迟要求</li>
                    <li><span class="star-label">T：</span>设计16阶FIR滤波器架构，实现ADC采集、算法处理与DAC输出的全流程开发</li>
                    <li><span class="star-label">A：</span>Xilinx Vivado开发｜Verilog HDL编码｜MATLAB算法仿真｜ModelSim功能验证｜AXI4总线协议</li>
                    <li><span class="star-label">R：</span>处理延迟&lt;2ms｜信噪比提升18dB｜资源利用率优化23%｜获课程设计优秀评价</li>
                </ul>
            </div>
        </div>
        
        <!-- 专业技能 -->
        <div class="section">
            <h2 class="section-title">专业技能</h2>
            <div class="skills-container">
                <div class="skill-category">
                    <h3 class="skill-title">硬件开发</h3>
                    <ul class="skill-items">
                        <li>Altium Designer（四层板设计）</li>
                        <li>STM32F1/F4系列开发</li>
                        <li>ESP32物联网开发</li>
                        <li>示波器/逻辑分析仪</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3 class="skill-title">嵌入式开发</h3>
                    <ul class="skill-items">
                        <li>C/C++（嵌入式方向）</li>
                        <li>FreeRTOS/RT-Thread</li>
                        <li>Keil MDK/IAR</li>
                        <li>Linux驱动基础</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3 class="skill-title">开发工具</h3>
                    <ul class="skill-items">
                        <li>Git版本控制</li>
                        <li>VSCode/PyCharm</li>
                        <li>MATLAB仿真</li>
                        <li>Jira项目管理</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3 class="skill-title">协议/标准</h3>
                    <ul class="skill-items">
                        <li>UART/SPI/I2C</li>
                        <li>Modbus/CAN</li>
                        <li>TCP/IP/MQTT</li>
                        <li>USB2.0协议</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 奖项荣誉 -->
        <div class="section">
            <h2 class="section-title">奖项荣誉</h2>
            <div class="skills-container">
                <div class="skill-category">
                    <ul class="skill-items">
                        <li>全国大学生电子设计竞赛二等奖（2023）</li>
                        <li>校级电子设计竞赛一等奖（2023）</li>
                        <li>国家奖学金（2022）</li>
                        <li>校级三好学生（2021-2022）</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 照片位置调整功能（实际使用时移除注释）
        /*
        function adjustPhotoPosition() {
            const photo = document.querySelector('.profile-photo');
            // 示例：向右移动10px
            photo.style.setProperty('--photo-right', '25mm'); 
        }
        */
    </script>
</body>
</html>