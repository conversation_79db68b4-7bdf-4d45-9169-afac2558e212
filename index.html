<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子信息工程应届生简历</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
        }
        
        body {
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.5;
            padding: 20px;
        }
        
        .resume {
            max-width: 210mm;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 25mm 20mm;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .name {
            font-size: 24px;
            font-weight: bold;
            color: #1a3b5c;
            margin-bottom: 10px;
        }
        
        .contact {
            font-size: 14px;
        }
        
        .section {
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1a3b5c;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        
        .item {
            margin-bottom: 12px;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .item-title {
            font-weight: bold;
        }
        
        .item-date {
            color: #666;
        }
        
        .item-role {
            font-style: italic;
            color: #666;
        }
        
        ul {
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            list-style-type: none;
            padding-left: 0;
        }
        
        .skills-category {
            font-weight: bold;
            margin-right: 5px;
        }
        
        .skills-item {
            margin-right: 20px;
            margin-bottom: 8px;
        }
        
        @media print {
            body {
                padding: 0;
                background-color: #fff;
            }
            
            .resume {
                box-shadow: none;
                padding: 15mm;
            }
        }
    </style>
</head>
<body>
    <div class="resume">
        <!-- 个人信息 -->
        <div class="header">
            <div class="name">姓名</div>
            <div class="contact">
                <span>电话：13800000000</span> | 
                <span>邮箱：<EMAIL></span> | 
                <span>意向城市：北京/上海</span>
            </div>
        </div>
        
        <!-- 求职意向 -->
        <div class="section">
            <div class="section-title">求职意向</div>
            <div>嵌入式软件开发工程师</div>
        </div>
        
        <!-- 教育背景 -->
        <div class="section">
            <div class="section-title">教育背景</div>
            <div class="item">
                <div class="item-header">
                    <span class="item-title">XX大学 | 电子信息工程 | 本科</span>
                    <span class="item-date">2020.09 - 2024.06</span>
                </div>
                <ul>
                    <li><strong>核心课程：</strong>数字电路、模拟电路、信号与系统、微机原理、C语言程序设计、嵌入式系统开发、数字信号处理</li>
                    <li><strong>GPA：</strong>3.7/4.0（专业排名：前15%）</li>
                </ul>
            </div>
        </div>
        
        <!-- 项目经验 -->
        <div class="section">
            <div class="section-title">项目经验</div>
            
            <!-- 项目1 -->
            <div class="item">
                <div class="item-header">
                    <span class="item-title">基于STM32的智能家居环境监测系统</span>
                    <span class="item-date">2023.03 - 2023.06</span>
                </div>
                <div class="item-role">项目负责人</div>
                <ul>
                    <li>设计基于STM32F103的环境监测节点，实现温湿度、光照度、气体浓度的实时采集与远程监控</li>
                    <li>使用Altium Designer完成四层PCB设计，负责硬件原理图设计与Layout</li>
                    <li>基于FreeRTOS开发多任务系统，实现传感器数据采集、Wi-Fi通信和本地显示功能</li>
                    <li>采用ESP8266 Wi-Fi模块实现数据上传云平台，响应时间<0.5s，功耗降低30%，设计通过EMC测试</li>
                </ul>
            </div>
            
            <!-- 项目2 -->
            <div class="item">
                <div class="item-header">
                    <span class="item-title">车载CAN总线通信系统</span>
                    <span class="item-date">2022.10 - 2023.01</span>
                </div>
                <div class="item-role">核心开发成员</div>
                <ul>
                    <li>设计基于STM32的车载CAN总线通信节点，实现车内多传感器数据的高可靠传输</li>
                    <li>使用C语言编写CAN驱动程序，实现数据帧的封装、发送、接收与解析</li>
                    <li>设计低功耗模式切换策略，系统待机功耗降至5mA以下</li>
                    <li>开发上位机监控软件（Qt），实现实时数据显示与故障诊断，通信成功率>99.9%</li>
                </ul>
            </div>
            
            <!-- 项目3 -->
            <div class="item">
                <div class="item-header">
                    <span class="item-title">[课程项目] 数字滤波器设计与实现</span>
                    <span class="item-date">2022.03 - 2022.06</span>
                </div>
                <div class="item-role">独立完成</div>
                <ul>
                    <li>使用MATLAB设计并仿真FIR与IIR数字滤波器，分析频率响应特性</li>
                    <li>基于TI DSP平台实现实时数字滤波算法，处理音频信号</li>
                    <li>优化算法结构，降低计算复杂度，处理延迟降低40%</li>
                </ul>
            </div>
        </div>
        
        <!-- 专业技能 -->
        <div class="section">
            <div class="section-title">专业技能</div>
            <ul class="skills-list">
                <li class="skills-item"><span class="skills-category">编程语言：</span>C（熟练）, C++（熟悉）, Python（了解）, MATLAB（熟悉）</li>
                <li class="skills-item"><span class="skills-category">硬件工具：</span>Altium Designer（原理图与四层板设计）, 示波器, 逻辑分析仪</li>
                <li class="skills-item"><span class="skills-category">开发平台：</span>STM32, ESP32/8266, TI DSP</li>
                <li class="skills-item"><span class="skills-category">软件/开发工具：</span>Keil MDK, IAR Workbench, FreeRTOS, Git（版本控制）</li>
                <li class="skills-item"><span class="skills-category">通信协议：</span>UART, SPI, I2C, CAN, TCP/IP, Wi-Fi</li>
            </ul>
        </div>
        
        <!-- 奖项与荣誉 -->
        <div class="section">
            <div class="section-title">奖项与荣誉</div>
            <ul>
                <li>全国大学生电子设计竞赛省级二等奖（2022.08）</li>
                <li>校"互联网+"创新创业大赛技术创新奖（2023.05）</li>
                <li>国家奖学金（2022.10）</li>
                <li>校级优秀学生（2021.09）</li>
            </ul>
        </div>
    </div>
</body>
</html>